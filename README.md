# 本地模拟接口服务

这是一个简单的Node.js Express服务器，为前端学习提供模拟API接口。

## 安装和运行

1. 安装依赖：
```bash
npm install
```

2. 启动服务器：
```bash
npm start
```

或者使用开发模式（自动重启）：
```bash
npm run dev
```

3. 服务器将在 `http://localhost:3000` 启动

## 获取你的IP地址

为了让局域网内的其他设备访问，你需要知道你的IP地址：

**Windows:**
```bash
ipconfig
```

**Mac/Linux:**
```bash
ifconfig
```

然后其他设备可以通过 `http://你的IP地址:3000` 访问接口。

## API接口文档

### 基础信息
- 基础URL: `http://localhost:3000` 或 `http://你的IP地址:3000`
- 所有接口都支持跨域访问
- 返回格式统一为JSON

### 用户管理接口

#### 1. 获取用户列表
- **URL:** `GET /api/users`
- **响应示例:**
```json
{
  "code": 200,
  "message": "获取用户列表成功",
  "data": [
    {
      "id": 1,
      "name": "张三",
      "email": "<EMAIL>",
      "age": 25
    }
  ]
}
```

#### 2. 获取单个用户
- **URL:** `GET /api/users/:id`
- **参数:** id (用户ID)
- **响应示例:**
```json
{
  "code": 200,
  "message": "获取用户成功",
  "data": {
    "id": 1,
    "name": "张三",
    "email": "<EMAIL>",
    "age": 25
  }
}
```

#### 3. 创建用户
- **URL:** `POST /api/users`
- **请求体:**
```json
{
  "name": "新用户",
  "email": "<EMAIL>",
  "age": 25
}
```

#### 4. 更新用户
- **URL:** `PUT /api/users/:id`
- **请求体:**
```json
{
  "name": "更新的用户名",
  "email": "<EMAIL>",
  "age": 30
}
```

#### 5. 删除用户
- **URL:** `DELETE /api/users/:id`

### 文章接口

#### 1. 获取文章列表
- **URL:** `GET /api/posts`

#### 2. 获取单个文章
- **URL:** `GET /api/posts/:id`

### 登录接口

#### 用户登录
- **URL:** `POST /api/login`
- **请求体:**
```json
{
  "username": "admin",
  "password": "123456"
}
```
- **响应示例:**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "mock-jwt-token-1234567890",
    "user": {
      "id": 1,
      "username": "admin",
      "name": "管理员"
    }
  }
}
```

## 前端测试示例

### 使用fetch获取用户列表：
```javascript
fetch('http://localhost:3000/api/users')
  .then(response => response.json())
  .then(data => console.log(data));
```

### 使用fetch创建用户：
```javascript
fetch('http://localhost:3000/api/users', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    name: '测试用户',
    email: '<EMAIL>',
    age: 25
  })
})
.then(response => response.json())
.then(data => console.log(data));
```

## 注意事项

1. 这是一个内存数据库，重启服务器后数据会重置
2. 服务器绑定到 `0.0.0.0`，允许局域网访问
3. 默认端口是3000，如果被占用会报错
4. 所有接口都已配置CORS，支持跨域请求
