const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3000;

// 中间件
app.use(cors()); // 允许跨域
app.use(express.json()); // 解析JSON请求体
app.use(express.urlencoded({ extended: true })); // 解析URL编码的请求体

// 模拟数据
let users = [
  { id: 1, name: '张三', email: 'z<PERSON><PERSON>@example.com', age: 25 },
  { id: 2, name: '李四', email: '<EMAIL>', age: 30 },
  { id: 3, name: '王五', email: '<EMAIL>', age: 28 }
];

let posts = [
  { id: 1, title: '第一篇文章', content: '这是第一篇文章的内容', author: '张三', createTime: '2024-01-01' },
  { id: 2, title: '第二篇文章', content: '这是第二篇文章的内容', author: '李四', createTime: '2024-01-02' },
  { id: 3, title: '第三篇文章', content: '这是第三篇文章的内容', author: '王五', createTime: '2024-01-03' }
];

// 根路径
app.get('/', (req, res) => {
  res.json({
    message: '欢迎使用模拟接口服务',
    version: '1.0.0',
    endpoints: {
      users: '/api/users',
      posts: '/api/posts',
      login: '/api/login'
    }
  });
});

// 用户相关接口
// 获取所有用户
app.get('/api/users', (req, res) => {
  res.json({
    code: 200,
    message: '获取用户列表成功',
    data: users
  });
});

// 根据ID获取用户
app.get('/api/users/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const user = users.find(u => u.id === id);
  
  if (user) {
    res.json({
      code: 200,
      message: '获取用户成功',
      data: user
    });
  } else {
    res.status(404).json({
      code: 404,
      message: '用户不存在'
    });
  }
});

// 创建用户
app.post('/api/users', (req, res) => {
  const { name, email, age } = req.body;
  
  if (!name || !email) {
    return res.status(400).json({
      code: 400,
      message: '姓名和邮箱不能为空'
    });
  }
  
  const newUser = {
    id: users.length + 1,
    name,
    email,
    age: age || 0
  };
  
  users.push(newUser);
  
  res.status(201).json({
    code: 201,
    message: '创建用户成功',
    data: newUser
  });
});

// 更新用户
app.put('/api/users/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const userIndex = users.findIndex(u => u.id === id);
  
  if (userIndex === -1) {
    return res.status(404).json({
      code: 404,
      message: '用户不存在'
    });
  }
  
  const { name, email, age } = req.body;
  users[userIndex] = { ...users[userIndex], name, email, age };
  
  res.json({
    code: 200,
    message: '更新用户成功',
    data: users[userIndex]
  });
});

// 删除用户
app.delete('/api/users/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const userIndex = users.findIndex(u => u.id === id);
  
  if (userIndex === -1) {
    return res.status(404).json({
      code: 404,
      message: '用户不存在'
    });
  }
  
  users.splice(userIndex, 1);
  
  res.json({
    code: 200,
    message: '删除用户成功'
  });
});

// 文章相关接口
// 获取所有文章
app.get('/api/posts', (req, res) => {
  res.json({
    code: 200,
    message: '获取文章列表成功',
    data: posts
  });
});

// 根据ID获取文章
app.get('/api/posts/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const post = posts.find(p => p.id === id);
  
  if (post) {
    res.json({
      code: 200,
      message: '获取文章成功',
      data: post
    });
  } else {
    res.status(404).json({
      code: 404,
      message: '文章不存在'
    });
  }
});

// 登录接口
app.post('/api/login', (req, res) => {
  const { username, password } = req.body;
  
  // 模拟登录验证
  if (username === 'admin' && password === '123456') {
    res.json({
      code: 200,
      message: '登录成功',
      data: {
        token: 'mock-jwt-token-' + Date.now(),
        user: {
          id: 1,
          username: 'admin',
          name: '管理员'
        }
      }
    });
  } else {
    res.status(401).json({
      code: 401,
      message: '用户名或密码错误'
    });
  }
});

// 启动服务器
app.listen(PORT, '0.0.0.0', () => {
  console.log(`服务器运行在 http://localhost:${PORT}`);
  console.log(`局域网访问地址: http://你的IP地址:${PORT}`);
  console.log('可用的接口:');
  console.log('  GET  /api/users     - 获取用户列表');
  console.log('  GET  /api/users/:id - 获取单个用户');
  console.log('  POST /api/users     - 创建用户');
  console.log('  PUT  /api/users/:id - 更新用户');
  console.log('  DELETE /api/users/:id - 删除用户');
  console.log('  GET  /api/posts     - 获取文章列表');
  console.log('  GET  /api/posts/:id - 获取单个文章');
  console.log('  POST /api/login     - 用户登录');
});
